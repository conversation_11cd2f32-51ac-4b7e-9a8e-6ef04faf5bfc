<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\controller\Frontend;
use app\common\service\PerformanceMonitor;
use app\common\service\CacheManager;
use app\common\service\ResponseAdapter;

/**
 * 性能监控控制器
 * 提供API性能统计和缓存状态查询
 */
class Performance extends Frontend
{
    protected array $noNeedLogin = [];
    protected array $noNeedPermission = [];

    /**
     * 获取完整性能报告
     */
    public function getReport()
    {
        try {
            $report = PerformanceMonitor::getPerformanceReport();
            
            // 添加缓存管理器的统计信息
            $cacheManager = new CacheManager();
            $report['cache_manager_stats'] = $cacheManager->getStats();
            
            return ResponseAdapter::success('获取成功', $report);
            
        } catch (\Exception $e) {
            return ResponseAdapter::error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取API统计信息
     */
    public function getApiStats()
    {
        try {
            $stats = PerformanceMonitor::getApiStats();
            return ResponseAdapter::success('获取成功', $stats);
            
        } catch (\Exception $e) {
            return ResponseAdapter::error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取SQL统计信息
     */
    public function getSqlStats()
    {
        try {
            $stats = PerformanceMonitor::getSqlStats();
            return ResponseAdapter::success('获取成功', $stats);
            
        } catch (\Exception $e) {
            return ResponseAdapter::error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public function getCacheStats()
    {
        try {
            $hitRate = PerformanceMonitor::getCacheHitRate();
            
            $cacheManager = new CacheManager();
            $managerStats = $cacheManager->getStats();
            
            $stats = [
                'hit_rate' => $hitRate,
                'redis_stats' => $managerStats
            ];
            
            return ResponseAdapter::success('获取成功', $stats);
            
        } catch (\Exception $e) {
            return ResponseAdapter::error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 清理性能监控数据
     */
    public function cleanup()
    {
        try {
            PerformanceMonitor::cleanup();
            return ResponseAdapter::success('清理完成');
            
        } catch (\Exception $e) {
            return ResponseAdapter::error('清理失败：' . $e->getMessage());
        }
    }
    
    /**
     * 重置性能监控数据
     */
    public function reset()
    {
        try {
            PerformanceMonitor::reset();
            return ResponseAdapter::success('重置完成');
            
        } catch (\Exception $e) {
            return ResponseAdapter::error('重置失败：' . $e->getMessage());
        }
    }
    
    /**
     * 启用/禁用性能监控
     */
    public function toggle()
    {
        try {
            $enabled = input('post.enabled/b', true);
            PerformanceMonitor::setEnabled($enabled);
            
            return ResponseAdapter::success($enabled ? '性能监控已启用' : '性能监控已禁用');
            
        } catch (\Exception $e) {
            return ResponseAdapter::error('操作失败：' . $e->getMessage());
        }
    }
}