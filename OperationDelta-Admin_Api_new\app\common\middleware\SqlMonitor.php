<?php
declare(strict_types=1);

namespace app\common\middleware;

use app\common\service\PerformanceMonitor;
use think\facade\Db;
use think\facade\Log;

/**
 * SQL监控中间件
 * 监控所有SQL查询的执行时间和性能
 */
class SqlMonitor
{
    public function handle($request, \Closure $next)
    {
        // 注册SQL监听
        $this->registerSqlListener();
        
        $response = $next($request);
        
        return $response;
    }
    
    /**
     * 注册SQL查询监听器
     */
    private function registerSqlListener(): void
    {
        // 监听SQL查询
        Db::listen(function ($sql, $time, $explain) {
            // 记录到性能监控
            PerformanceMonitor::logSql($sql, [], $time);
            
            // 记录慢查询详细信息
            if ($time > 0.1) { // 超过100ms的查询
                Log::warning('慢SQL查询详情', [
                    'sql' => $sql,
                    'time' => $time,
                    'explain' => $explain,
                    'memory_usage' => memory_get_usage(true),
                    'request_uri' => request()->url(true)
                ]);
            }
        });
    }
}